package io.gigsta.di

import io.gigsta.data.repository.AuthRepositoryImpl
import io.gigsta.data.repository.HistoryRepositoryImpl
import io.gigsta.data.repository.EmailApplicationRepositoryImpl
import io.gigsta.data.repository.ResumeRepositoryImpl
import io.gigsta.data.datasource.EmailApiService
import io.gigsta.data.datasource.ResumeApiService
import io.gigsta.domain.repository.AuthRepository
import io.gigsta.domain.repository.HistoryRepository
import io.gigsta.domain.repository.EmailApplicationRepository
import io.gigsta.domain.repository.ResumeRepository
import io.gigsta.domain.usecase.GetHistoryItemsUseCase
import io.gigsta.domain.usecase.SignInUseCase
import io.gigsta.domain.usecase.SignUpUseCase
import io.gigsta.domain.usecase.SignOutUseCase
import io.gigsta.domain.usecase.GenerateEmailApplicationUseCase
import io.gigsta.domain.usecase.UploadResumeUseCase
import io.gigsta.domain.usecase.GetExistingResumeUseCase
import io.gigsta.domain.usecase.DeleteResumeUseCase
import io.gigsta.domain.usecase.GetResumeUrlUseCase
import io.gigsta.presentation.auth.AuthViewModel
import io.gigsta.presentation.home.HomeViewModel
import io.gigsta.presentation.email.EmailApplicationViewModel

object AppModule {

    // Repositories
    private val authRepository: AuthRepository by lazy {
        AuthRepositoryImpl()
    }

    private val historyRepository: HistoryRepository by lazy {
        HistoryRepositoryImpl(authRepository)
    }

    private val emailApiService: EmailApiService by lazy {
        EmailApiService()
    }

    private val emailApplicationRepository: EmailApplicationRepository by lazy {
        EmailApplicationRepositoryImpl(emailApiService)
    }

    private val resumeApiService: ResumeApiService by lazy {
        ResumeApiService()
    }

    private val resumeRepository: ResumeRepository by lazy {
        ResumeRepositoryImpl(resumeApiService)
    }

    // Use Cases
    private val signInUseCase: SignInUseCase by lazy {
        SignInUseCase(authRepository)
    }

    private val signUpUseCase: SignUpUseCase by lazy {
        SignUpUseCase(authRepository)
    }

    private val signOutUseCase: SignOutUseCase by lazy {
        SignOutUseCase(authRepository)
    }

    private val getHistoryItemsUseCase: GetHistoryItemsUseCase by lazy {
        GetHistoryItemsUseCase(historyRepository)
    }

    private val generateEmailApplicationUseCase: GenerateEmailApplicationUseCase by lazy {
        GenerateEmailApplicationUseCase(emailApplicationRepository)
    }

    private val uploadResumeUseCase: UploadResumeUseCase by lazy {
        UploadResumeUseCase(resumeRepository)
    }

    private val getExistingResumeUseCase: GetExistingResumeUseCase by lazy {
        GetExistingResumeUseCase(resumeRepository)
    }

    private val deleteResumeUseCase: DeleteResumeUseCase by lazy {
        DeleteResumeUseCase(resumeRepository)
    }

    private val getResumeUrlUseCase: GetResumeUrlUseCase by lazy {
        GetResumeUrlUseCase(resumeRepository)
    }

    // ViewModels
    fun provideAuthViewModel(): AuthViewModel {
        return AuthViewModel(
            authRepository = authRepository,
            signInUseCase = signInUseCase,
            signUpUseCase = signUpUseCase,
            signOutUseCase = signOutUseCase
        )
    }

    fun provideHomeViewModel(): HomeViewModel {
        return HomeViewModel(getHistoryItemsUseCase)
    }

    fun provideEmailApplicationViewModel(): EmailApplicationViewModel {
        return EmailApplicationViewModel(
            generateEmailApplicationUseCase = generateEmailApplicationUseCase,
            uploadResumeUseCase = uploadResumeUseCase,
            getExistingResumeUseCase = getExistingResumeUseCase,
            deleteResumeUseCase = deleteResumeUseCase,
            getResumeUrlUseCase = getResumeUrlUseCase
        )
    }
}
